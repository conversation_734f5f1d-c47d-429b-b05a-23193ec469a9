import {tryit} from "radash";
import axios from "axios";
import {parseCsvStream} from "../../functions/stream/parseCsvStream.js";
import {getMappingConfiguration} from "../../functions/mappings/getMappingConfiguration.js";
import {mappingData} from "../../functions/mappings/mappingData.js";
import {mergeAdditionalData} from "../../functions/mergeAdditionalData.js";
import {campaignKeyExistsInFile} from "../../functions/campaignKeyExistsInFile.js";
import {assignCampaignKey} from "../../functions/assignCampaignKey.js";
import _ from "lodash";
import {startPreConfigurations} from "../../functions/preconfigurations/startPreConfigurations.js";
import {upsertData} from "../../functions/upsertData.js";
import {docmqConfig} from "../../docmq-config.js";

export const transfersTask = async (job, db) => {
    const jobId = job._id;
    console.log({jobId}, "Starting transfersTask processing");

    try {
        // Validate job structure
        if (!job || !job.payload) {
            throw new Error("Invalid job: missing payload");
        }

        const {payload} = job;
        let totalUpsertedCount = 0;
        let totalModifiedCount = 0;

        // Validate required payload fields
        const {originalFileName, path, fileName, mappingConfiguration} = payload;

        if (!originalFileName || !path || !fileName || !mappingConfiguration) {
            throw new Error(`Missing required payload fields: originalFileName=${!!originalFileName}, path=${!!path}, fileName=${!!fileName}, mappingConfiguration=${!!mappingConfiguration}`);
        }

        const {db_config, mongo} = mappingConfiguration;

        if (!db_config || !mongo) {
            throw new Error("Invalid mappingConfiguration: missing db_config or mongo");
        }

        console.log({
            jobId,
            originalFileName,
            fileName,
            path
        }, "Processing transfer task");

        const folderSplit = originalFileName.replaceAll('.csv', '');
        const fileUrl = `${docmqConfig.databroker.uri}/public/${path}/${folderSplit}/${fileName}`;
        console.log({jobId, fileUrl}, "Fetching file from data broker");
        const encodedUrl = encodeURI(fileUrl);
        const [error, response] = await tryit(axios)({
            url: encodedUrl,
            method: 'GET',
            responseType: 'stream',
            timeout: 30000, // 30 second timeout
        });
        if (error) {
            console.error({
                jobId,
                fileName,
                fileUrl,
                error: error.message,
                status: error.response?.status,
                statusText: error.response?.statusText
            }, "Error fetching file from data broker");
        }
        const stream =  response.data;
        console.log({jobId, fileName}, "Parsing CSV stream");
        const fileRows = await parseCsvStream(stream, 0, '£');
        if (!fileRows || fileRows.length === 0) {
            console.warn({jobId, fileName}, "File has no rows to process");
            return { success: true, rowsProcessed: 0, message: "No rows to process" };
        }

        console.log({jobId, fileName, rowCount: fileRows.length}, "CSV parsed successfully");

        // Get mapping configuration
        const {
            constMappingConfiguration,
            preConfigurationMethods,
            mappingColumnsConfiguration
        } = getMappingConfiguration(mappingConfiguration);

        // Map data
        console.log({jobId}, "Mapping data");
        let mappedData = _.map(fileRows, (item) => {
            return mappingData(mappingColumnsConfiguration, item);
        });

        // Merge additional data
        mappedData = mergeAdditionalData(mappedData, constMappingConfiguration);

        // Handle campaign key
        const {hasCampaignKey, campaignKey} = campaignKeyExistsInFile(fileRows);
        if (hasCampaignKey) {
            assignCampaignKey(mappedData, campaignKey);
            console.log({jobId, campaignKey}, "Campaign key assigned");
        }

        console.log({jobId}, "Starting pre-configurations");

        // Get vendors and apply pre-configurations
        const vendors = await db.collection('vendors').find({}).toArray();
        mappedData = await startPreConfigurations(preConfigurationMethods, mappedData, vendors);

        // Upsert data
        const collection = 'transfers_new_task';
        const mongoConfiguration = mongo.configuration;

        console.log({
            jobId,
            collection,
            dataCount: mappedData.length,
            mongoConfiguration
        }, "Upserting data to database");

        const result = await upsertData(
            collection,
            mappedData,
            mongoConfiguration,
            fileName,
            totalUpsertedCount,
            totalModifiedCount,
            mappingConfiguration
        );

        console.log({
            jobId,
            fileName,
            result,
            rowsProcessed: fileRows.length
        }, "Transfer task completed successfully");

        return {
            success: true,
            rowsProcessed: fileRows.length,
            result
        };

    } catch (error) {
        console.error({
            jobId,
            error: error.message,
            stack: error.stack,
            payload: job.payload ? {
                fileName: job.payload.fileName,
                originalFileName: job.payload.originalFileName,
                path: job.payload.path
            } : 'missing'
        }, "Transfer task failed");

        // Re-throw the error so the consumer can handle retries
        throw error;
    }
}
