import _ from "lodash";
import {docmqConfig} from "../../docmq-config.js";
import {mongoConnect} from "../../mongoConnect.js";

export const addFilename = async (items, collection, filename) => {
    const {db} = await mongoConnect(docmqConfig.database.database)
    const ids = _.map(items, '_id');
    const filter = {_id: {$in: ids}};
    const update = {
        $addToSet: {filenames: filename}
    };
    await db.collection(collection).updateMany(filter, update);
}
