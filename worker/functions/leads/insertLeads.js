import _ from "lodash";
import {mongoConnect} from "../../mongoConnect.js";
import {docmqConfig} from "../../docmq-config.js";

export const insertLeads = async (leadsToInsert) => {
    const {db} = await mongoConnect(docmqConfig.database.database)
    const operations = _.map(leadsToInsert, lead => ({
        updateOne: {
            filter: { _id: lead._id },
            update: { $set: lead },
            upsert: true
        }
    }));
    await db.collection('leads_test').bulkWrite(operations);
}
