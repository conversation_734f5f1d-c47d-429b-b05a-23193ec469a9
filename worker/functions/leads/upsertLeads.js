import _ from "lodash";
import {mongoConnect} from "../../mongoConnect.js";
import {docmqConfig} from "../../docmq-config.js";


export const upsertLeads = async (leadsToInsert) => {
    const {db} = await mongoConnect(docmqConfig.database.database)
    const bulk = _.map(leadsToInsert, lead => {
        return {
            updateOne: {
                filter: {vendor_id: lead.vendor_id, phone: lead.phone},
                update: {
                    $setOnInsert: {_id: lead._id},
                    $set: _.omit(lead, "_id")
                },
                upsert: true
            }
        }
    })
    if (bulk && bulk.length > 0) {
        await db.collection('leads_test').bulkWrite(bulk)
    }
}
