import {DateTime} from "luxon";

// Colección configurable via environment variable
// const TASK_QUEUES_COLLECTION = process.env.DATABROKER_QUEUES_COLLECTION || 'databroker_queues';
const TASK_QUEUES_COLLECTION =  'databroker_queues_test';
export const validateStatusTrace = async (traceId, db) => {
    console.log(`🔄 [${new Date().toISOString()}] Validando trace: ${traceId}`);

    try {
        // Primero verificar el estado actual para debugging
        const currentTrace = await db.collection(TASK_QUEUES_COLLECTION).findOne({ _id: traceId });
        console.log(`📋 Estado actual antes de incrementar: ${JSON.stringify(currentTrace, null, 2)}`);

        // Simplificar la condición y agregar logging
        console.log(`🔍 Intentando incrementar con condición: status='pending' AND completedJobs < totalJobs`);
        console.log(`🔍 Valores: status="${currentTrace?.status}", completedJobs=${currentTrace?.completedJobs}, totalJobs=${currentTrace?.totalJobs}`);
        console.log(`🔍 Condición cumplida: ${currentTrace?.status === 'pending' && currentTrace?.completedJobs < currentTrace?.totalJobs}`);

        const result = await db.collection(TASK_QUEUES_COLLECTION).findOneAndUpdate(
            {
                _id: traceId,
                status: 'pending',
                completedJobs: { $lt: currentTrace?.totalJobs }  // Usar comparación simple
            },
            {
                $inc: { completedJobs: 1 }
            },
            {
                returnDocument: 'after'
            }
        );

        console.log(`📊 Resultado del findOneAndUpdate: ${result.value ? 'SUCCESS' : 'FAILED'}`);
        if (result.value) {
            console.log(`📊 Nuevo estado: ${JSON.stringify(result.value, null, 2)}`);
        }

        if (!result.value) {
            // Verificar si el trace existe pero ya está completed
            const existingTrace = await db.collection(TASK_QUEUES_COLLECTION).findOne({ _id: traceId });

            if (!existingTrace) {
                console.log(`❌ Trace ${traceId} NO EXISTE en la colección ${TASK_QUEUES_COLLECTION}`);
            } else if (existingTrace.status === 'completed') {
                console.log(`✅ Trace ${traceId} ya fue completado por otro worker (${existingTrace.completedJobs}/${existingTrace.totalJobs})`);
            } else {
                console.log(`⚠️  Trace ${traceId} existe pero status: ${existingTrace.status} (${existingTrace.completedJobs}/${existingTrace.totalJobs})`);
            }
            return;
        }

        const trace = result.value;
        console.log(`Job completado para trace ${traceId}: ${trace.completedJobs}/${trace.totalJobs}`);

        // DEBUG: Mostrar comparación exacta
        console.log(`🔍 COMPARACIÓN: ${trace.completedJobs} >= ${trace.totalJobs} = ${trace.completedJobs >= trace.totalJobs}`);
        console.log(`🔍 TIPOS: completedJobs(${typeof trace.completedJobs}) totalJobs(${typeof trace.totalJobs})`);
        console.log(`🔍 VALORES RAW: completedJobs="${trace.completedJobs}" totalJobs="${trace.totalJobs}"`);

        console.log(`🚨 PUNTO CRÍTICO: Evaluando condición para trace ${traceId}`);

        if (trace.completedJobs >= trace.totalJobs) {
            console.log(`🎯 ✅ ENTRA A CONDICIÓN: Actualizando trace ${traceId} a 'completed' (${trace.completedJobs}/${trace.totalJobs})`);

            // Verificar el estado actual del documento antes del update
            const currentDoc = await db.collection(TASK_QUEUES_COLLECTION).findOne({ _id: traceId });
            console.log(`📋 Estado actual del trace antes del update:`, JSON.stringify(currentDoc, null, 2));

            const nowDate = DateTime.now().setZone('America/Chicago');
            const dateInUTC = nowDate.setZone("utc");

            console.log(`🔍 Intentando update con condición: { _id: "${traceId}", status: "pending" }`);
            console.log(`📅 Fecha UTC a establecer:`, dateInUTC);

            const finalUpdate = await db.collection(TASK_QUEUES_COLLECTION).updateOne(
                {
                    _id: traceId,
                    status: 'pending'  // Solo verificar que aún esté pending
                },
                {
                    $set: {
                        status: 'completed',
                        processed_by: 'worker-docmq',
                        completed_at: dateInUTC
                    }
                }
            );

            console.log(`📊 Update result: matchedCount=${finalUpdate.matchedCount}, modifiedCount=${finalUpdate.modifiedCount}`);

            // Verificar el estado después del update
            const updatedDoc = await db.collection(TASK_QUEUES_COLLECTION).findOne({ _id: traceId });
            console.log(`📋 Estado del trace después del update:`, JSON.stringify(updatedDoc, null, 2));

            if (finalUpdate.modifiedCount > 0) {
                console.log(`✅ Trace ${traceId} completado - todos los ${trace.totalJobs} jobs terminaron`);
            } else {
                console.log(`⚠️  Trace ${traceId} NO se pudo actualizar - matchedCount: ${finalUpdate.matchedCount}`);
                if (finalUpdate.matchedCount === 0) {
                    console.log(`❌ PROBLEMA: No se encontró documento con _id: ${traceId} y status: 'pending'`);
                }
            }
        } else {
            console.log(`🚨 ❌ NO ENTRA A CONDICIÓN: ${trace.completedJobs} >= ${trace.totalJobs} es FALSE`);
            console.log(`⏳ Trace ${traceId} aún no completo: ${trace.completedJobs}/${trace.totalJobs}`);
        }

    } catch (error) {
        console.error(`❌ Error validando trace ${traceId}:`, error.message);
    }
}
