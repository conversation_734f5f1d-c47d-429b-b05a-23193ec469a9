import {docmqConfig} from "../../docmq-config.js";
import {mongoConnect} from "../../mongoConnect.js";

export const getLeadsWithCorrectLeadId = async (groupedTransfers) => {
    const {db} = await mongoConnect(docmqConfig.database.database)
    const transfersWithLeadsIds = new Set(groupedTransfers.transfersWithLeadsIds || []);
    const transfers = transfersWithLeadsIds.size > 0 ? Array.from(transfersWithLeadsIds) : [];
    const leadIds = transfers.map(transfer => transfer.lead_id);
    const filter = {_id: {$in: leadIds}}
    return await db.collection('leads')
        .find(filter)
        .sort({date_created: -1})
        .toArray();
}
