

import _ from "lodash";
import {generateId} from "../generateId";
import {fillVendorId, fillVendorName} from "../vendors/vendorsStaticValues.js";
import {
    assignMissingInformationToTransfersWithCorrectLeadId
} from "../transfers/assignMissingInformationToTransfersWithCorrectLeadId.js";
import {
    assignMissingInformationToTransfersWithoutCorrectLeadId
} from "../transfers/assignMissingInformationToTransfersWithoutCorrectLeadId.js";
import {fetchLeads} from "../transfers/fetchLeads.js";
import {getLeads} from "../leads/getLeads.js";
import {upsertLeads} from "../leads/upsertLeads.js";



export const fixTransfers = async (mappedData, args) => {
    console.log('fixTransfers init')
    if (mappedData && mappedData.length > 0) {
        let {
            transfersWithCreateStrategy,
            transfersWithMatchStrategy
        } = getTransferWithCreateLeadStrategy(mappedData, args.vendors)
        console.log('transfersWithCreateStrategy: ', transfersWithCreateStrategy.length)
        console.log('transfersWithMatchStrategy: ', transfersWithMatchStrategy.length)
        if (transfersWithMatchStrategy && transfersWithMatchStrategy.length > 0) {
            console.log('Fetch leads..')
            const {leadsWithCorrectLeadIds, leadsWithoutCorrectLeadId} = await fetchLeads(transfersWithMatchStrategy);
            console.log('Fetch leads end..')
            console.log('assign missing info')
            transfersWithMatchStrategy = await assignMissingInformationToTransfersWithCorrectLeadId(transfersWithMatchStrategy, leadsWithCorrectLeadIds, leadsWithoutCorrectLeadId, args)
            transfersWithMatchStrategy = await assignMissingInformationToTransfersWithoutCorrectLeadId(transfersWithMatchStrategy, leadsWithoutCorrectLeadId, args);
            console.log('assign missing info end')
        }
        if (transfersWithCreateStrategy && transfersWithCreateStrategy.length > 0) {
            console.log('Processing transfers with create strategy')
            const newLeads = [];
            const phones = _.compact(_.map(transfersWithCreateStrategy, transfer => transfer?.consumer_phone));
            const leads = await getLeads({phone: {$in: phones}});
            const {defaultVendorKey} = args;
            _.forEach(transfersWithCreateStrategy, (item, index) => {
                item['createStrategy'] = true;
                const leadFound = leads.find(lead => lead.phone === item.consumer_phone && lead.vendor_id === fillVendorId)
                item['lead_id'] = leadFound?._id
                if (!leadFound) {
                    const newLeadId = generateId();
                    const subId2 = item['lead_id'];
                    item['lead_id'] = newLeadId;
                    const lead = {
                        _id: newLeadId,
                        phone: item?.consumer_phone,
                        date_created: item.completed_date,
                        subid: item.sub_id != null ? item.sub_id : '',
                        pubid: item.pub_id != null ? item.pub_id : '',
                        subid2: subId2,
                        campaign_key: item?.campaign_key != null ? item.campaign_key : '',
                        vendor_key: defaultVendorKey ? defaultVendorKey : 'unknown',
                        email: item.email,
                        vendor: fillVendorName,
                        vendor_id: fillVendorId
                    }
                    newLeads.push(lead);
                }
            })
            console.log(`creating ${newLeads.length} leads for transfers with create strategy`)
            await upsertLeads(newLeads)
        }
        return transfersWithMatchStrategy.concat(transfersWithCreateStrategy);
    }
    console.log('fixTransfers end')
}

const getTransferWithCreateLeadStrategy = (mappedData, vendors) => {
    if (_.get(vendors, '[0].pub_ids[0]', false) && _.isObject(vendors[0].pub_ids[0])) {
        console.log('Object structure')
        const pubIds = _.reduce(vendors, (acc, vendor) => {
            _.forEach(vendor.pub_ids, pub => {
                if (pub.leadStrategy === 'create') {
                    acc.create.push(pub.id);
                } else if (pub.leadStrategy === 'match') {
                    acc.match.push(pub.id);
                }
            });
            return acc;
        }, {create: [], match: []});
        const transfersWithCreateStrategy = _.filter(mappedData, item =>
            _.includes(pubIds.create, item.pub_id)
        );
        const transfersWithMatchStrategy = _.filter(mappedData, item =>
            !_.includes(pubIds.create, item.pub_id)
        );

        return {transfersWithCreateStrategy, transfersWithMatchStrategy};
    } else {
        const transfersWithCreateStrategy = _.filter(mappedData, {pub_id: 'DRP-LivMed'});
        const transfersWithMatchStrategy = _.filter(mappedData, item => item.pub_id !== 'DRP-LivMed');
        return {transfersWithCreateStrategy, transfersWithMatchStrategy};
    }
}

