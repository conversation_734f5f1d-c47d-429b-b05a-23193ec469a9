import _ from "lodash";
import {docmqConfig} from "../../docmq-config.js";
import {mongoConnect} from "../../mongoConnect.js";


export const removeIgnoredPhones = async (mappedData, args) => {
    const {db} = await mongoConnect(docmqConfig.database.database)
    const blackListPhonesResult = await db.collection('blacklist_phones').findOne({collection: 'transfers'});
    if (blackListPhonesResult && blackListPhonesResult.phones && blackListPhonesResult.phones.length > 0) {
        const phones = blackListPhonesResult.phones ? blackListPhonesResult.phones : [];
        return _.filter(mappedData, item =>
            !_.some(phones, num => _.includes(item.transferred_to, num))
        );
    } else {
        return mappedData;
    }

}
