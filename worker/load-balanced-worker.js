#!/usr/bin/env node

/**
 * Worker que se registra con el Load Balancer Distribuido
 */

import {Queue} from 'docmq';
import {createDriver, createLogger, docmqConfig, getFileProcessingQueueConfig} from './docmq-config.js';
import {MongoDeduplication} from './mongodb-deduplication.js';
import {DatabaseMetrics} from './database-metrics.js';
import os from 'os';
import {HttpServer} from "./server.js";
import {transfersTask} from "./tasks/transfers/transfersTask.js";
import {mongoConnect} from "./mongoConnect.js";
import {DateTime} from 'luxon';

class LoadBalancedWorker {
    constructor(options = {}) {
        const hostname = os.hostname();
        this.instanceId = `lb-worker-${hostname}`;
        this.concurrency = docmqConfig.queues.file_processing_tasks.processor.concurrency
        this.serverInfo = options.serverInfo || {};

        this.processingTasksCollection = process.env.PROCESSING_TASKS_COLLECTION || 'file_processing_tasks';

        this.logger = createLogger(`LBWorker-${this.instanceId}`);

        this.metrics = null;

        this.deduplication = new MongoDeduplication({
            workerId: this.instanceId,
            ttl: 3600
        });

        this.mongoClient = null;

        this.driver = null;
        this.queue = null;
        this.isRunning = false;

        this.heartbeatTimer = null;
        this.cleanupTimer = null;

        this.httpServer = new HttpServer(this);
        this.apiPort = options.apiPort || process.env.PORT || 3000;
    }

    async initialize() {
        try {
            this.logger.info(`🚀 Inicializando worker load-balanced`);

            // Conectar a MongoDB para stats
            const {client, db} = await mongoConnect()
            this.db = db;
            this.mongoClient = client;

            // Inicializar métricas de base de datos
            this.metrics = new DatabaseMetrics({
                instanceId: this.instanceId,
                db: this.db,
                concurrency: this.concurrency
            });
            await this.metrics.initialize();

            // Inicializar deduplicación
            await this.deduplication.initialize();

            // Crear driver y cola DocMQ
            this.driver = await createDriver();
            const queueConfig = getFileProcessingQueueConfig();
            this.queue = new Queue(this.driver, queueConfig.name, queueConfig.options);

            this.logger.success(`✅ Worker load-balanced inicializado`);
            return true;
        } catch (error) {
            this.logger.error(`❌ Error inicializando worker: ${error.message}`);
            throw error;
        }
    }

    async registerWithLoadBalancer() {
        if (this.metrics) {
            await this.metrics.registerWithLoadBalancer();
        }
    }

    async processTask(job, api) {
        const jobId = api.ref;
        const startTime = Date.now();

        await this.metrics.recordJobStart();

        try {

            const atomicUpdate = await this.db.collection(this.processingTasksCollection).updateOne(
                {
                    _id: job._id,
                    status: {$nin: ['processing', 'processed']}
                },
                {
                    $set: {
                        status: 'processing',
                        processing_at: DateTime.now().toISO()
                    },
                    $addToSet: {processed_by: this.instanceId}
                }
            );
            if (atomicUpdate.matchedCount === 0) {
                await this.metrics.recordJobSkipped('Already being processed by another worker');
                await api.ack({
                    success: true,
                    skipped: true,
                    reason: 'Already being processed by another worker',
                    workerId: this.instanceId
                });

                return {success: true, skipped: true};
            }

            // Verificación y reclamación atómica del job
            const {processed, claimed} = await this.deduplication.isProcessed(jobId);

            if (processed) {
                await this.metrics.recordJobSkipped('Already processed or claimed by another worker');
                await api.ack({
                    success: true,
                    skipped: true,
                    reason: 'Already processed or claimed by another worker',
                    workerId: this.instanceId
                });

                return {success: true, skipped: true};
            }

            if (claimed) {
                this.logger.info(`🔒 Job ${jobId} reclamado - iniciando procesamiento`);
            }
            // Procesar job
            const currentStats = await this.metrics.getWorkerStats();
            this.logger.info(`🔄 [${currentStats.activeTasks}/${this.concurrency}] Procesando: ${jobId}`);

            const {result} = await transfersTask(job.originalDocument, this.db);
            await this.db.collection(this.processingTasksCollection).updateOne(
                {_id: job._id},
                {$set: {status: 'processed'}, $addToSet: {processed_by: this.instanceId}},
            );
            await this.deduplication.markAsProcessed(jobId, {
                instanceId: this.instanceId
            });
            // Incrementar contador de jobs completados (sin validar completitud)
            if (job.originalDocument && job.originalDocument.trace_id) {
                const traceId = job.originalDocument.trace_id;
                const databrokerCollection = process.env.DATABROKER_QUEUES_COLLECTION || 'databroker_queues_test';
                const totalUpsertedCount =  result?.totalUpsertedCount ?? 0;
                const totalModifiedCount = result?.totalModifiedCount ?? 0;
                try {
                    await this.db.collection(databrokerCollection).updateOne(
                        {_id: traceId, status: 'pending'},
                        {
                            $inc: {
                                completedJobs: 1,
                                totalUpsertedCount: totalUpsertedCount,
                                totalModifiedCount: totalModifiedCount
                            }
                        }
                    );
                    this.logger.info(`📊 Contador incrementado para trace: ${traceId}`);
                } catch (error) {
                    this.logger.error(`❌ Error incrementando contador trace ${traceId}: ${error.message}`);
                }
            }

            // NOTA: Validación de completitud movida al Producer para evitar race conditions

            const processingTime = Date.now() - startTime;

            await this.metrics.recordJobComplete(processingTime);

            const resultData = {
                success: true,
                processedAt: new Date().toISOString(),
                workerId: this.instanceId,
                processingTime,
                jobRef: jobId
            };

            const updatedStats = await this.metrics.getWorkerStats();
            this.logger.success(`✅ Job ${jobId} completado en ${processingTime}ms - Total: ${updatedStats.processedTasks}`);

            await api.ack(resultData);
            return resultData;

        } catch (error) {
            await this.metrics.recordJobError(error);
            this.logger.error(`❌ Error procesando ${jobId}: ${error.message}`);
            await api.fail(error);
            throw error;
        }
    }


    async start() {
        try {
            await this.initialize();
            this.isRunning = true;

            // Iniciar servidor HTTP API
            await this.httpServer.start();

            this.showWorkerInfo();

            await this.registerWithLoadBalancer();

            this.queue.process(async (job, api) => {
                    return await this.processTask(job, api);
                },
                {
                    concurrency: this.concurrency,
                    visibility: parseInt(process.env.DOCMQ_VISIBILITY_TIMEOUT) || 600, // 10 min para evitar timeouts prematuros
                    pollInterval: parseInt(process.env.DOCMQ_POLL_INTERVAL) || 2000 // 2 segundos, menos agresivo
                }
            );

            this.logger.success(`✅ Worker load-balanced iniciado con ${this.concurrency} concurrencia`);

            this.heartbeatTimer = setInterval(async () => {
                if (this.isRunning) {
                    try {
                        await this.registerWithLoadBalancer();
                    } catch (error) {
                        this.logger.error(`❌ Error en heartbeat: ${error.message}`);
                    }
                }
            }, 60000);

            this.cleanupTimer = setInterval(async () => {
                if (this.isRunning) {
                    try {
                        const cleanupResult = await this.deduplication.cleanupStuckJobs(10);
                        if (cleanupResult.freed > 0) {
                            this.logger.info(`🔄 ${cleanupResult.freed} jobs liberados para reprocesamiento`);
                        }

                        // Limpiar locks expirados si existe el método
                        if (this.deduplication.cleanupExpiredLocks) {
                            await this.deduplication.cleanupExpiredLocks();
                        }
                    } catch (error) {
                        this.logger.error(`❌ Error en cleanup: ${error.message}`);
                    }
                }
            }, 10 * 60 * 1000);


            // Manejo de señales
            process.on('SIGINT', () => this.gracefulShutdown());
            process.on('SIGTERM', () => this.gracefulShutdown());

        } catch (error) {
            this.logger.error('❌ Error iniciando worker:', error.message);
            process.exit(1);
        }
    }


    showWorkerInfo() {
        console.log('='.repeat(80));
        console.log('🌐 WORKER LOAD-BALANCED WITH API');
        console.log('='.repeat(80));
        console.log(`📋 Instance ID: ${this.instanceId}`);
        console.log(`🔄 Concurrencia: ${this.concurrency}`);
        console.log(`🖥️  Servidor: ${os.hostname()}`);
        console.log(`🖥️  CPUs: ${os.cpus().length}`);
        console.log(`💾 Memoria: ${Math.round(os.totalmem() / 1024 / 1024 / 1024)}GB total, ${Math.round(os.freemem() / 1024 / 1024 / 1024)}GB libre`);
        console.log(`🌐 Load Balancer: Registrado automáticamente`);
        console.log(`🚀 API Server: http://localhost:${this.apiPort}/api`);
        console.log(`📊 Health Check: http://localhost:${this.apiPort}/api/health`);
        console.log(`📈 Worker Stats: http://localhost:${this.apiPort}/api/worker/stats`);
        console.log(`⏱️  Iniciado: ${new Date().toLocaleString()}`);
        console.log(`🔧 Visibility Timeout: ${parseInt(process.env.DOCMQ_VISIBILITY_TIMEOUT) || 600}s`);
        console.log(`🔄 Poll Interval: ${parseInt(process.env.DOCMQ_POLL_INTERVAL) || 2000}ms`);
        console.log(`🕐 Verificación huérfanos: en Producer (instancia única)`);
        console.log('='.repeat(80));
        console.log('🎯 Worker registrado con Load Balancer distribuido...\n');
    }


    async gracefulShutdown() {
        this.logger.info('🛑 Iniciando cierre graceful...');
        this.isRunning = false;

        try {
            // Limpiar timers
            if (this.heartbeatTimer) {
                clearInterval(this.heartbeatTimer);
                this.heartbeatTimer = null;
            }
            if (this.cleanupTimer) {
                clearInterval(this.cleanupTimer);
                this.cleanupTimer = null;
            }
            // orphanCheckTimer removido - ahora en Producer

            // Esperar tareas activas con timeout
            const maxWaitTime = 30000; // 30 segundos máximo
            const startWait = Date.now();
            let currentStats = await this.metrics.getWorkerStats();

            while (currentStats.activeTasks > 0 && (Date.now() - startWait) < maxWaitTime) {
                this.logger.info(`⏳ Esperando ${currentStats.activeTasks} tareas activas...`);
                await new Promise(resolve => setTimeout(resolve, 1000));
                currentStats = await this.metrics.getWorkerStats();
            }

            if (currentStats.activeTasks > 0) {
                this.logger.warn(`⚠️  Forzando cierre con ${currentStats.activeTasks} tareas activas`);
            }

            // Cleanup de métricas
            if (this.metrics) {
                await this.metrics.cleanup();
            }

            // Cerrar conexiones
            if (this.queue) {
                await this.queue.remove()
                this.logger.info('📦 Cola DocMQ cerrada');
            }

            if (this.deduplication) {
                await this.deduplication.close();
                this.logger.info('🔌 Deduplicación cerrada');
            }

            if (this.mongoClient) {
                await this.mongoClient.close();
                this.logger.info('🔌 Cliente MongoDB cerrado');
            }

            this.logger.success('✅ Worker load-balanced cerrado correctamente');
            process.exit(0);
        } catch (error) {
            this.logger.error('❌ Error en cierre graceful:', error.message);
            process.exit(1);
        }
    }
}

// Ejecutar si es llamado directamente
if (import.meta.url === `file://${process.argv[1]}`) {
    const options = {};
    const worker = new LoadBalancedWorker(options);
    worker.start();
}

export {LoadBalancedWorker};
