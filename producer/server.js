import { Hono } from "hono";
import { cors } from "hono/cors";
import { logger } from "hono/logger";
import { prettyJSO<PERSON> } from "hono/pretty-json";
import { serve } from "@hono/node-server";
import os from 'os';

export class HttpServer {
    constructor(producer = null) {
        this.app = new Hono().basePath("/api");
        this.port = process.env.PORT || 3001;
        this.isRunning = false;
        this.server = null;
        this.producer = producer; // Referencia al producer para acceder a estadísticas

        this.setupMiddleware();
        this.setupRoutes();
    }

    setupMiddleware() {
        // CORS para permitir requests desde frontend
        this.app.use("/*", cors({
            origin: "*",
            allowMethods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
            allowHeaders: ["Content-Type", "Authorization"],
        }));

        // Logger para requests
        this.app.use("/*", logger());

        // Pretty JSON para responses más legibles
        this.app.use("/*", prettyJSON());
    }

    setupRoutes() {
        // Health check básico
        this.app.get("/health", (c) => {
            return c.json({
                status: "ok",
                service: "stream-docmq-producer",
                version: process.env.VERSION || "1.0.0",
                timestamp: new Date().toISOString(),
                uptime: process.uptime(),
                environment: process.env.NODE_ENV || "development"
            });
        });

        // Información del producer
        this.app.get("/producer/info", (c) => {
            const producerInfo = {
                mode: this.producer?.mode || "unknown",
                isRunning: this.producer?.isRunning || false,
                jobsCreated: this.producer?.jobsCreated || 0,
                startTime: this.producer?.startTime || null,
                mongoUri: this.producer?.mongoUri ? "***configured***" : "not configured",
                databaseName: this.producer?.databaseName || "unknown",
                collectionName: this.producer?.collectionName || "unknown",
                serverInfo: {
                    hostname: os.hostname(),
                    platform: os.platform(),
                    cpus: os.cpus().length,
                    totalMemory: Math.round(os.totalmem() / 1024 / 1024 / 1024), // GB
                    freeMemory: Math.round(os.freemem() / 1024 / 1024 / 1024),   // GB
                    loadAverage: os.loadavg(),
                    nodeVersion: process.version,
                    pid: process.pid
                }
            };

            return c.json(producerInfo);
        });

        // Estadísticas del producer
        this.app.get("/producer/stats", (c) => {
            if (!this.producer) {
                return c.json({ error: "Producer not initialized" }, 503);
            }

            const uptime = this.producer.startTime ?
                Math.floor((Date.now() - this.producer.startTime) / 1000) : 0;
            const jobsPerMinute = uptime > 0 ?
                Math.round((this.producer.jobsCreated / uptime) * 60) : 0;

            const stats = {
                jobsCreated: this.producer.jobsCreated || 0,
                uptime: uptime,
                jobsPerMinute: jobsPerMinute,
                startTime: this.producer.startTime,
                mode: this.producer.mode,
                isRunning: this.producer.isRunning || false,
                changeStreamActive: this.producer.changeStream ? true : false
            };

            return c.json(stats);
        });

        // Lista de endpoints disponibles
        this.app.get("/", (c) => {
            const endpoints = {
                service: "stream-docmq-producer",
                version: process.env.VERSION || "1.0.0",
                endpoints: {
                    health: "GET /api/health",
                    producerInfo: "GET /api/producer/info",
                    producerStats: "GET /api/producer/stats",
                    producerStatus: "GET /api/producer/status",
                    metrics: "GET /api/metrics"
                },
                documentation: "https://github.com/your-username/stream-docmq"
            };

            return c.json(endpoints);
        });
    }

    async start() {
        if (this.isRunning) {
            console.warn("⚠️  HTTP server is already running");
            return;
        }

        try {
            this.isRunning = true;

            // Iniciar servidor HTTP usando @hono/node-server
            this.server = serve({
                fetch: this.app.fetch,
                port: this.port,
            });

            console.log(`🌐 Producer API server started at http://localhost:${this.port}/api`);
            console.log(`📊 Health check: http://localhost:${this.port}/api/health`);
            console.log(`📈 Producer stats: http://localhost:${this.port}/api/producer/stats`);
            console.log(`📋 All endpoints: http://localhost:${this.port}/api`);

            return {
                port: this.port,
                fetch: this.app.fetch,
                server: this.server
            };
        } catch (error) {
            console.error("❌ Failed to start HTTP server:", error);
            this.isRunning = false;
            throw error;
        }
    }

    async stop() {
        if (!this.isRunning) {
            console.warn("⚠️  HTTP server is not running");
            return;
        }

        try {
            if (this.server) {
                this.server.close();
                console.log("🛑 HTTP server stopped");
            }
            this.isRunning = false;
        } catch (error) {
            console.error("❌ Error stopping HTTP server:", error);
            throw error;
        }
    }

    setProducer(producer) {
        this.producer = producer;
    }

    getServerConfig() {
        return {
            port: this.port,
            fetch: this.app.fetch,
        };
    }
}

export default HttpServer
